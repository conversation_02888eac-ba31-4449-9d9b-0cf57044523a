import React from 'react'
import Card from './Card'
import Button from './Button'

const Services = () => {
  const services = [
    {
      icon: '🏠',
      title: 'Domestic Skip Hire',
      description: 'Perfect for home renovations, garden clearances, and household waste disposal.',
      features: ['2-8 yard skips', 'Same day delivery', 'Flexible hire periods', 'Competitive pricing'],
      popular: false
    },
    {
      icon: '🏢',
      title: 'Commercial Skip Hire',
      description: 'Reliable waste management solutions for businesses and construction sites.',
      features: ['10-40 yard skips', 'Regular collections', 'Waste management plans', 'Licensed disposal'],
      popular: true
    },
    {
      icon: '♻️',
      title: 'Recycling Services',
      description: 'Eco-friendly waste disposal with maximum recycling and minimal landfill.',
      features: ['90% recycling rate', 'Waste sorting', 'Environmental reports', 'Green credentials'],
      popular: false
    },
    {
      icon: '🚛',
      title: 'Grab Hire',
      description: 'Efficient grab lorry services for large volumes of waste and materials.',
      features: ['16-20 tonne capacity', 'Easy access', 'Quick loading', 'Cost effective'],
      popular: false
    }
  ]

  const scrollToBooking = () => {
    const element = document.querySelector('#booking')
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section id="services" className="section-padding bg-gray-50">
      <div className="container-max">
        {/* Header */}
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900">
            Our <span className="text-gradient">Services</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive waste management solutions tailored to your specific needs. 
            From small domestic projects to large commercial operations.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {services.map((service, index) => (
            <Card 
              key={index} 
              className={`relative ${service.popular ? 'ring-2 ring-primary-500' : ''}`}
            >
              {service.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-primary-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </span>
                </div>
              )}
              
              <div className="text-center space-y-4">
                <div className="text-4xl">{service.icon}</div>
                <h3 className="text-xl font-bold text-gray-900">{service.title}</h3>
                <p className="text-gray-600">{service.description}</p>
                
                <ul className="space-y-2 text-sm">
                  {service.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center space-x-2">
                      <span className="w-4 h-4 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-primary-600 text-xs">✓</span>
                      </span>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button 
                  variant={service.popular ? 'primary' : 'outline'} 
                  size="sm"
                  onClick={scrollToBooking}
                  className="w-full"
                >
                  Book Now
                </Button>
              </div>
            </Card>
          ))}
        </div>

        {/* Additional Info */}
        <div className="bg-white rounded-2xl shadow-lg p-8 lg:p-12">
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            <div className="space-y-6">
              <h3 className="text-3xl font-bold text-gray-900">
                Why Choose WeWantWaste?
              </h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mt-1">
                    <span className="text-primary-600 text-sm">⚡</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Fast Response</h4>
                    <p className="text-gray-600">Same day delivery available for urgent requirements</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mt-1">
                    <span className="text-primary-600 text-sm">💰</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Competitive Pricing</h4>
                    <p className="text-gray-600">Transparent pricing with no hidden costs</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mt-1">
                    <span className="text-primary-600 text-sm">🌱</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">Eco-Friendly</h4>
                    <p className="text-gray-600">90% of waste diverted from landfill through recycling</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-xl p-8 text-center">
              <div className="space-y-4">
                <div className="text-6xl">📞</div>
                <h4 className="text-2xl font-bold text-gray-900">Need Help Choosing?</h4>
                <p className="text-gray-600">
                  Our expert team is here to help you find the perfect skip size and service for your project.
                </p>
                <Button onClick={scrollToBooking}>
                  Get Free Quote
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Services
