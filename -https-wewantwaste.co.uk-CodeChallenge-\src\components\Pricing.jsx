import React, { useState } from 'react'
import Card from './Card'
import But<PERSON> from './Button'

const Pricing = () => {
  const [selectedCategory, setSelectedCategory] = useState('domestic')

  const pricingData = {
    domestic: [
      {
        size: '2 Yard Skip',
        price: '£120',
        duration: '1 week',
        description: 'Perfect for small household clearances',
        features: ['Holds 20-25 bin bags', 'Ideal for bathroom renovations', 'Garden waste', 'Small DIY projects'],
        popular: false
      },
      {
        size: '4 Yard Skip',
        price: '£160',
        duration: '1 week',
        description: 'Great for medium household projects',
        features: ['Holds 40-50 bin bags', 'Kitchen renovations', 'Garden clearances', 'Furniture disposal'],
        popular: true
      },
      {
        size: '6 Yard Skip',
        price: '£200',
        duration: '1 week',
        description: 'Ideal for larger home renovations',
        features: ['Holds 60-70 bin bags', 'Whole room clearances', 'Large garden projects', 'Home extensions'],
        popular: false
      },
      {
        size: '8 Yard Skip',
        price: '£240',
        duration: '1 week',
        description: 'Perfect for major home projects',
        features: ['Holds 80-90 bin bags', 'Multiple room clearances', 'Large renovations', 'House clearances'],
        popular: false
      }
    ],
    commercial: [
      {
        size: '12 Yard Skip',
        price: '£280',
        duration: '1 week',
        description: 'Ideal for small commercial projects',
        features: ['Construction waste', 'Office clearances', 'Retail fit-outs', 'Small building projects'],
        popular: false
      },
      {
        size: '20 Yard Skip',
        price: '£350',
        duration: '1 week',
        description: 'Perfect for large commercial use',
        features: ['Major construction', 'Industrial waste', 'Large office moves', 'Demolition projects'],
        popular: true
      },
      {
        size: '30 Yard Skip',
        price: '£420',
        duration: '1 week',
        description: 'For major commercial operations',
        features: ['Large construction sites', 'Industrial clearances', 'Major demolitions', 'Bulk waste disposal'],
        popular: false
      },
      {
        size: '40 Yard Skip',
        price: '£500',
        duration: '1 week',
        description: 'Maximum capacity for large projects',
        features: ['Massive construction projects', 'Industrial operations', 'Large-scale demolitions', 'Bulk materials'],
        popular: false
      }
    ]
  }

  const scrollToBooking = () => {
    const element = document.querySelector('#booking')
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section id="pricing" className="section-padding bg-white">
      <div className="container-max">
        {/* Header */}
        <div className="text-center space-y-4 mb-12">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900">
            Transparent <span className="text-gradient">Pricing</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            No hidden costs, no surprises. Our competitive pricing includes delivery, 
            collection, and responsible waste disposal.
          </p>
        </div>

        {/* Category Selector */}
        <div className="flex justify-center mb-12">
          <div className="bg-gray-100 rounded-lg p-1 flex">
            <button
              onClick={() => setSelectedCategory('domestic')}
              className={`px-6 py-3 rounded-md font-semibold transition-all duration-300 ${
                selectedCategory === 'domestic'
                  ? 'bg-white text-primary-600 shadow-md'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Domestic
            </button>
            <button
              onClick={() => setSelectedCategory('commercial')}
              className={`px-6 py-3 rounded-md font-semibold transition-all duration-300 ${
                selectedCategory === 'commercial'
                  ? 'bg-white text-primary-600 shadow-md'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Commercial
            </button>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {pricingData[selectedCategory].map((plan, index) => (
            <Card 
              key={index}
              className={`relative text-center ${plan.popular ? 'ring-2 ring-primary-500 scale-105' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-primary-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </span>
                </div>
              )}
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{plan.size}</h3>
                  <p className="text-gray-600 text-sm">{plan.description}</p>
                </div>
                
                <div>
                  <div className="text-4xl font-bold text-primary-600">{plan.price}</div>
                  <div className="text-gray-600">per {plan.duration}</div>
                </div>
                
                <ul className="space-y-3 text-sm">
                  {plan.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center space-x-2">
                      <span className="w-4 h-4 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-primary-600 text-xs">✓</span>
                      </span>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button 
                  variant={plan.popular ? 'primary' : 'outline'}
                  onClick={scrollToBooking}
                  className="w-full"
                >
                  Book Now
                </Button>
              </div>
            </Card>
          ))}
        </div>

        {/* Additional Information */}
        <div className="grid lg:grid-cols-2 gap-8">
          <Card className="bg-gradient-to-br from-primary-50 to-primary-100">
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-gray-900">What's Included</h3>
              <ul className="space-y-3">
                <li className="flex items-center space-x-3">
                  <span className="w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">✓</span>
                  </span>
                  <span className="text-gray-700">Free delivery and collection</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">✓</span>
                  </span>
                  <span className="text-gray-700">Responsible waste disposal</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">✓</span>
                  </span>
                  <span className="text-gray-700">Environmental recycling</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">✓</span>
                  </span>
                  <span className="text-gray-700">Flexible hire periods</span>
                </li>
                <li className="flex items-center space-x-3">
                  <span className="w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm">✓</span>
                  </span>
                  <span className="text-gray-700">24/7 customer support</span>
                </li>
              </ul>
            </div>
          </Card>
          
          <Card className="bg-gradient-to-br from-secondary-50 to-secondary-100">
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-gray-900">Special Offers</h3>
              <div className="space-y-4">
                <div className="bg-white rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900">First Time Customer</h4>
                  <p className="text-gray-600 text-sm">10% off your first skip hire</p>
                  <span className="text-secondary-600 font-bold">Save up to £50</span>
                </div>
                <div className="bg-white rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900">Multiple Skips</h4>
                  <p className="text-gray-600 text-sm">15% off when booking 3+ skips</p>
                  <span className="text-secondary-600 font-bold">Bulk discount</span>
                </div>
                <div className="bg-white rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900">Extended Hire</h4>
                  <p className="text-gray-600 text-sm">20% off hires over 4 weeks</p>
                  <span className="text-secondary-600 font-bold">Long-term savings</span>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gray-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Need a Custom Quote?
            </h3>
            <p className="text-gray-600 mb-6">
              For unique requirements, multiple skips, or long-term contracts, 
              get in touch for a personalized quote.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button onClick={scrollToBooking}>
                Get Custom Quote
              </Button>
              <Button variant="outline" onClick={() => window.open('tel:+441234567890')}>
                Call Now: 01234 567890
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Pricing
