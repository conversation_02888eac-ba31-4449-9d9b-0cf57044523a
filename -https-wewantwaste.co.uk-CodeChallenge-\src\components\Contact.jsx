import React, { useState } from 'react'
import Card from './Card'
import Button from './Button'

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const contactInfo = [
    {
      icon: '📞',
      title: 'Phone',
      details: '+44 1234 567890',
      description: '24/7 Customer Support',
      action: () => window.open('tel:+441234567890')
    },
    {
      icon: '✉️',
      title: 'Email',
      details: '<EMAIL>',
      description: 'Quick Response Guaranteed',
      action: () => window.open('mailto:<EMAIL>')
    },
    {
      icon: '📍',
      title: 'Address',
      details: '123 Waste Management Way',
      description: 'London, UK SW1A 1AA',
      action: () => window.open('https://maps.google.com/?q=123+Waste+Management+Way+London+UK')
    },
    {
      icon: '🕒',
      title: 'Hours',
      details: 'Mon-Fri: 7AM-7PM',
      description: 'Sat-Sun: 8AM-5PM',
      action: null
    }
  ]

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000))
      setIsSubmitted(true)
      console.log('Contact form submitted:', formData)
    } catch (error) {
      console.error('Submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isSubmitted) {
    return (
      <section id="contact" className="section-padding bg-white">
        <div className="container-max">
          <Card className="max-w-2xl mx-auto text-center">
            <div className="space-y-6">
              <div className="text-6xl">✅</div>
              <h2 className="text-3xl font-bold text-gray-900">Message Sent!</h2>
              <p className="text-gray-600">
                Thank you for contacting us. We'll get back to you within 24 hours.
              </p>
              <Button onClick={() => setIsSubmitted(false)}>
                Send Another Message
              </Button>
            </div>
          </Card>
        </div>
      </section>
    )
  }

  return (
    <section id="contact" className="section-padding bg-white">
      <div className="container-max">
        {/* Header */}
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900">
            Get In <span className="text-gradient">Touch</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Have questions about our services? Need a custom quote? 
            Our friendly team is here to help you find the perfect waste management solution.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Contact Information</h3>
              <div className="grid sm:grid-cols-2 gap-6">
                {contactInfo.map((info, index) => (
                  <Card 
                    key={index} 
                    className={`text-center ${info.action ? 'cursor-pointer' : ''}`}
                    onClick={info.action}
                  >
                    <div className="space-y-3">
                      <div className="text-3xl">{info.icon}</div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{info.title}</h4>
                        <p className="text-primary-600 font-medium">{info.details}</p>
                        <p className="text-gray-600 text-sm">{info.description}</p>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <Card className="bg-gradient-to-br from-primary-50 to-secondary-50">
              <div className="space-y-6">
                <h4 className="text-xl font-bold text-gray-900">Quick Actions</h4>
                <div className="space-y-4">
                  <Button 
                    className="w-full justify-center"
                    onClick={() => window.open('tel:+441234567890')}
                  >
                    📞 Call Now for Immediate Help
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-center"
                    onClick={() => {
                      const element = document.querySelector('#booking')
                      if (element) element.scrollIntoView({ behavior: 'smooth' })
                    }}
                  >
                    📋 Book Skip Online
                  </Button>
                  <Button 
                    variant="ghost" 
                    className="w-full justify-center"
                    onClick={() => window.open('https://wa.me/441234567890')}
                  >
                    💬 WhatsApp Us
                  </Button>
                </div>
              </div>
            </Card>

            {/* Emergency Contact */}
            <Card className="bg-red-50 border border-red-200">
              <div className="text-center space-y-4">
                <div className="text-3xl">🚨</div>
                <h4 className="text-xl font-bold text-red-900">Emergency Service</h4>
                <p className="text-red-700">
                  Need urgent skip collection or have an emergency waste situation?
                </p>
                <Button 
                  variant="danger"
                  onClick={() => window.open('tel:+441234567890')}
                >
                  Emergency Hotline: 01234 567890
                </Button>
              </div>
            </Card>
          </div>

          {/* Contact Form */}
          <Card>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6">Send Us a Message</h3>
              </div>

              <div className="grid sm:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                    placeholder="Enter your name"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                    placeholder="Enter your email"
                  />
                </div>
              </div>

              <div className="grid sm:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                    placeholder="Enter your phone"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Subject *
                  </label>
                  <select
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                  >
                    <option value="">Select a subject</option>
                    <option value="general-inquiry">General Inquiry</option>
                    <option value="quote-request">Quote Request</option>
                    <option value="booking-support">Booking Support</option>
                    <option value="complaint">Complaint</option>
                    <option value="feedback">Feedback</option>
                    <option value="partnership">Partnership Opportunity</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Message *
                </label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  required
                  rows={6}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                  placeholder="Tell us how we can help you..."
                />
              </div>

              <div className="text-center">
                <Button
                  type="submit"
                  size="lg"
                  disabled={isSubmitting}
                  className="w-full sm:w-auto"
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </Button>
                <p className="text-sm text-gray-600 mt-4">
                  We'll respond to your message within 24 hours.
                </p>
              </div>
            </form>
          </Card>
        </div>

        {/* Map Section */}
        <div className="mt-16">
          <Card padding="none" className="overflow-hidden">
            <div className="bg-gradient-to-r from-primary-600 to-secondary-600 text-white p-8 text-center">
              <h3 className="text-2xl font-bold mb-4">Visit Our Facility</h3>
              <p className="mb-6">
                Come and see our modern waste processing facility and meet our team in person.
              </p>
              <Button 
                variant="outline" 
                className="border-white text-white hover:bg-white hover:text-primary-600"
                onClick={() => window.open('https://maps.google.com/?q=123+Waste+Management+Way+London+UK')}
              >
                Get Directions
              </Button>
            </div>
            <div className="h-64 bg-gray-200 flex items-center justify-center">
              <div className="text-center text-gray-600">
                <div className="text-4xl mb-4">🗺️</div>
                <p>Interactive map would be embedded here</p>
                <p className="text-sm">123 Waste Management Way, London, UK SW1A 1AA</p>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </section>
  )
}

export default Contact
