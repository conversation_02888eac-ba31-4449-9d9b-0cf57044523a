import React from 'react'
import Card from './Card'
import Button from './Button'

const About = () => {
  const stats = [
    { number: '15+', label: 'Years Experience', icon: '📅' },
    { number: '5000+', label: 'Happy Customers', icon: '😊' },
    { number: '90%', label: 'Waste Recycled', icon: '♻️' },
    { number: '24/7', label: 'Customer Support', icon: '📞' }
  ]

  const values = [
    {
      icon: '🌱',
      title: 'Environmental Responsibility',
      description: 'We\'re committed to protecting the environment by maximizing recycling and minimizing landfill waste.'
    },
    {
      icon: '⚡',
      title: 'Reliable Service',
      description: 'Fast, dependable skip hire services with same-day delivery available for urgent requirements.'
    },
    {
      icon: '💰',
      title: 'Fair Pricing',
      description: 'Transparent, competitive pricing with no hidden costs. What you see is what you pay.'
    },
    {
      icon: '🏆',
      title: 'Quality Guarantee',
      description: 'Licensed, insured, and committed to providing the highest quality waste management services.'
    }
  ]

  const team = [
    {
      name: '<PERSON>',
      role: 'Managing Director',
      image: '👩‍💼',
      description: '15 years in waste management, passionate about sustainable solutions.'
    },
    {
      name: '<PERSON>',
      role: 'Operations Manager',
      image: '👨‍🔧',
      description: 'Ensures smooth operations and timely deliveries across all services.'
    },
    {
      name: '<PERSON> <PERSON>',
      role: 'Customer Service Lead',
      image: '👩‍💻',
      description: 'Dedicated to providing exceptional customer experiences and support.'
    }
  ]

  const scrollToContact = () => {
    const element = document.querySelector('#contact')
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section id="about" className="section-padding bg-gray-50">
      <div className="container-max">
        {/* Header */}
        <div className="text-center space-y-4 mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900">
            About <span className="text-gradient">WeWantWaste</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Your trusted partner for professional skip hire and waste management solutions. 
            Serving communities and businesses with reliable, eco-friendly services since 2008.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <Card key={index} className="text-center">
              <div className="space-y-4">
                <div className="text-4xl">{stat.icon}</div>
                <div className="text-3xl font-bold text-primary-600">{stat.number}</div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            </Card>
          ))}
        </div>

        {/* Story Section */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          <div className="space-y-6">
            <h3 className="text-3xl font-bold text-gray-900">Our Story</h3>
            <div className="space-y-4 text-gray-600">
              <p>
                Founded in 2008, WeWantWaste began as a small family business with a simple mission: 
                to provide reliable, affordable skip hire services while protecting our environment.
              </p>
              <p>
                Over the years, we've grown to become one of the region's most trusted waste management 
                companies, serving thousands of satisfied customers across domestic and commercial sectors.
              </p>
              <p>
                Our commitment to sustainability drives everything we do. We've invested heavily in 
                recycling facilities and processes, achieving an industry-leading 90% waste diversion 
                rate from landfills.
              </p>
            </div>
            <Button onClick={scrollToContact}>
              Get In Touch
            </Button>
          </div>
          
          <div className="relative">
            <Card className="bg-gradient-to-br from-primary-50 to-secondary-50">
              <div className="text-center space-y-6 p-8">
                <div className="text-6xl">🚛</div>
                <h4 className="text-2xl font-bold text-gray-900">Modern Fleet</h4>
                <p className="text-gray-600">
                  Our modern, well-maintained fleet ensures reliable service and minimal environmental impact.
                </p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="bg-white rounded-lg p-3">
                    <div className="font-semibold text-primary-600">50+</div>
                    <div className="text-gray-600">Vehicles</div>
                  </div>
                  <div className="bg-white rounded-lg p-3">
                    <div className="font-semibold text-primary-600">Euro 6</div>
                    <div className="text-gray-600">Standards</div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Values */}
        <div className="mb-16">
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">Our Values</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center">
                <div className="space-y-4">
                  <div className="text-4xl">{value.icon}</div>
                  <h4 className="text-xl font-semibold text-gray-900">{value.title}</h4>
                  <p className="text-gray-600">{value.description}</p>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Team */}
        <div className="mb-16">
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">Meet Our Team</h3>
          <div className="grid md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <Card key={index} className="text-center">
                <div className="space-y-4">
                  <div className="text-6xl">{member.image}</div>
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900">{member.name}</h4>
                    <p className="text-primary-600 font-medium">{member.role}</p>
                  </div>
                  <p className="text-gray-600">{member.description}</p>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Certifications */}
        <Card className="bg-white">
          <div className="text-center space-y-8">
            <h3 className="text-3xl font-bold text-gray-900">Certifications & Compliance</h3>
            <div className="grid md:grid-cols-4 gap-8">
              <div className="space-y-2">
                <div className="text-3xl">🏅</div>
                <h4 className="font-semibold text-gray-900">ISO 14001</h4>
                <p className="text-gray-600 text-sm">Environmental Management</p>
              </div>
              <div className="space-y-2">
                <div className="text-3xl">📋</div>
                <h4 className="font-semibold text-gray-900">Waste Carrier License</h4>
                <p className="text-gray-600 text-sm">Fully Licensed Operations</p>
              </div>
              <div className="space-y-2">
                <div className="text-3xl">🛡️</div>
                <h4 className="font-semibold text-gray-900">Fully Insured</h4>
                <p className="text-gray-600 text-sm">Comprehensive Coverage</p>
              </div>
              <div className="space-y-2">
                <div className="text-3xl">⭐</div>
                <h4 className="font-semibold text-gray-900">5-Star Rated</h4>
                <p className="text-gray-600 text-sm">Customer Satisfaction</p>
              </div>
            </div>
            <p className="text-gray-600 max-w-2xl mx-auto">
              We maintain the highest standards of safety, environmental responsibility, and customer service. 
              All our operations are fully licensed, insured, and compliant with industry regulations.
            </p>
          </div>
        </Card>
      </div>
    </section>
  )
}

export default About
