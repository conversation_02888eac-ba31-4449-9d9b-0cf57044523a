@tailwind base;
@tailwind components;
@tailwind utilities;

/* Define custom CSS variables for colors */
:root {
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;

  --color-secondary-50: #fefce8;
  --color-secondary-100: #fef9c3;
  --color-secondary-200: #fef08a;
  --color-secondary-300: #fde047;
  --color-secondary-400: #facc15;
  --color-secondary-500: #eab308;
  --color-secondary-600: #ca8a04;
  --color-secondary-700: #a16207;
  --color-secondary-800: #854d0e;
  --color-secondary-900: #713f12;
}

/* Custom base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.6;
  }
}

/* Custom component styles */
.btn-primary {
  background-color: var(--color-primary-600);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  transform: scale(1);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  background-color: var(--color-primary-700);
  transform: scale(1.05);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: var(--color-secondary-500);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  transform: scale(1);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  background-color: var(--color-secondary-600);
  transform: scale(1.05);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.btn-outline {
  border: 2px solid var(--color-primary-600);
  color: var(--color-primary-600);
  background-color: transparent;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.btn-outline:hover {
  background-color: var(--color-primary-600);
  color: white;
}

.card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  transform: translateY(0);
}

.card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateY(-0.25rem);
}

.section-padding {
  padding: 4rem 1rem;
}

@media (min-width: 640px) {
  .section-padding {
    padding: 4rem 1.5rem;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding: 4rem 2rem;
  }
}

.container-max {
  max-width: 80rem;
  margin: 0 auto;
}

.gradient-bg {
  background: linear-gradient(to bottom right, var(--color-primary-50), white, var(--color-secondary-50));
}

.text-gradient {
  background: linear-gradient(to right, var(--color-primary-600), var(--color-secondary-600));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Custom utilities */
@layer utilities {
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
  
  .animation-delay-600 {
    animation-delay: 600ms;
  }
}
