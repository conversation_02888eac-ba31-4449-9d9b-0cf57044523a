# WeWantWaste Skip Hire Redesign

A complete redesign of the WeWantWaste skip hire website, built from scratch with modern web technologies while maintaining all essential functionality for waste management services.

## 📋 Project Overview

This project is a comprehensive redesign of the WeWantWaste skip hire service website, created as part of a code challenge. The goal was to create a completely different visual design while preserving all core functionality for booking skip hire services, viewing pricing, and contacting the company.

### 🎯 Design Goals
- **Modern, Clean Interface**: Contemporary design with smooth animations and responsive layout
- **Enhanced User Experience**: Intuitive navigation and streamlined booking process
- **Environmental Focus**: Highlighting the company's commitment to recycling and sustainability
- **Mobile-First Approach**: Fully responsive design optimized for all devices
- **Accessibility**: WCAG compliant design with proper contrast and keyboard navigation

## 🚀 Features Implemented

### Core Functionality
- ✅ **Skip Hire Booking System**: Complete booking form with validation
- ✅ **Service Showcase**: Detailed information about domestic and commercial services
- ✅ **Pricing Calculator**: Interactive pricing display for different skip sizes
- ✅ **Contact Forms**: Multiple contact methods including emergency services
- ✅ **Company Information**: About section with team, values, and certifications

### User Experience Enhancements
- ✅ **Smooth Scrolling Navigation**: Seamless section-to-section navigation
- ✅ **Interactive Animations**: Custom Tailwind animations for engaging experience
- ✅ **Form Validation**: Real-time validation with user-friendly error messages
- ✅ **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- ✅ **Loading States**: Proper feedback during form submissions

### Technical Features
- ✅ **Modern React Architecture**: Component-based structure with hooks
- ✅ **Tailwind CSS Styling**: Utility-first CSS with custom design system
- ✅ **Vite Build System**: Fast development and optimized production builds
- ✅ **ES6+ JavaScript**: Modern JavaScript features and best practices

## 🛠️ Technology Stack

### Frontend Framework
- **React 19.1.0**: Latest React with modern hooks and features
- **Vite 6.3.5**: Next-generation frontend build tool for fast development

### Styling & Design
- **Tailwind CSS 4.1.8**: Utility-first CSS framework with custom configuration
- **PostCSS**: CSS processing with autoprefixer
- **Inter Font**: Modern, readable typography from Google Fonts

### Development Tools
- **@vitejs/plugin-react**: Vite plugin for React support with Fast Refresh
- **ESLint**: Code linting for consistent code quality
- **Modern ES Modules**: Native ES module support

### Custom Design System
- **Primary Colors**: Blue gradient palette (#0ea5e9 to #0c4a6e)
- **Secondary Colors**: Yellow/amber palette (#eab308 to #713f12)
- **Custom Animations**: Fade-in, slide-up, and gentle bounce effects
- **Responsive Breakpoints**: Mobile-first design with Tailwind's responsive system

## 📁 Project Structure

```
-https-wewantwaste.co.uk-CodeChallenge-/
├── public/                          # Static assets
├── src/                            # Source code
│   ├── components/                 # React components
│   │   ├── Header.jsx             # Navigation and branding
│   │   ├── Hero.jsx               # Landing section with CTA
│   │   ├── Services.jsx           # Service showcase
│   │   ├── BookingForm.jsx        # Skip booking form
│   │   ├── Pricing.jsx            # Pricing display
│   │   ├── About.jsx              # Company information
│   │   ├── Contact.jsx            # Contact forms and info
│   │   ├── Footer.jsx             # Footer with links
│   │   ├── Button.jsx             # Reusable button component
│   │   └── Card.jsx               # Reusable card component
│   ├── App.jsx                    # Main application component
│   ├── main.jsx                   # React application entry point
│   └── index.css                  # Global styles and Tailwind imports
├── index.html                     # HTML template
├── package.json                   # Dependencies and scripts
├── tailwind.config.js             # Tailwind CSS configuration
├── postcss.config.js              # PostCSS configuration
├── vite.config.js                 # Vite build configuration
└── README.md                      # Project documentation
```

## 📝 Files Created (June 9, 2025)

All the following files were created from scratch as part of this redesign project:

### Core Application Files
- **`src/main.jsx`** - React application entry point with StrictMode
- **`src/App.jsx`** - Main application component with routing structure
- **`src/index.css`** - Global styles, Tailwind imports, and custom CSS classes

### Component Architecture
- **`src/components/Header.jsx`** - Responsive navigation with mobile menu and smooth scrolling
- **`src/components/Hero.jsx`** - Landing section with animated elements and call-to-action
- **`src/components/Services.jsx`** - Service showcase with interactive cards and features
- **`src/components/BookingForm.jsx`** - Complete booking form with validation and submission
- **`src/components/Pricing.jsx`** - Interactive pricing display with category switching
- **`src/components/About.jsx`** - Company information, team, values, and certifications
- **`src/components/Contact.jsx`** - Contact forms, information, and emergency services
- **`src/components/Footer.jsx`** - Comprehensive footer with links and company details

### Utility Components
- **`src/components/Button.jsx`** - Reusable button component with multiple variants
- **`src/components/Card.jsx`** - Reusable card component with hover effects

### Configuration Updates
- **`postcss.config.js`** - Updated to use @tailwindcss/postcss plugin for Tailwind v4 compatibility

## 🚀 Setup and Installation

### Prerequisites
- Node.js (version 16 or higher)
- npm or yarn package manager

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone https://github.com/MorokaPrince/-https-wewantwaste.co.uk-CodeChallenge-.git
   cd -https-wewantwaste.co.uk-CodeChallenge-
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   Navigate to `http://localhost:5173` (or the port shown in terminal)

### Available Scripts

- **`npm run dev`** - Start development server with hot reload
- **`npm run build`** - Build for production
- **`npm run preview`** - Preview production build locally

## 🎨 Design Decisions and Modifications

### Visual Design Changes
- **Color Scheme**: Moved from original branding to a modern blue/yellow palette
- **Typography**: Implemented Inter font for improved readability and modern feel
- **Layout**: Switched to a single-page application with smooth scrolling navigation
- **Cards & Components**: Introduced card-based design with subtle shadows and hover effects
- **Animations**: Added custom Tailwind animations for enhanced user engagement

### User Experience Improvements
- **Mobile-First Design**: Completely responsive layout optimized for all screen sizes
- **Interactive Elements**: Hover states, loading indicators, and smooth transitions
- **Form Validation**: Real-time validation with clear error messaging
- **Navigation**: Sticky header with smooth scrolling to sections
- **Accessibility**: Proper contrast ratios, keyboard navigation, and semantic HTML

### Functional Enhancements
- **Booking System**: Enhanced form with better validation and user feedback
- **Service Display**: Interactive service cards with detailed feature lists
- **Pricing Calculator**: Dynamic pricing display with category switching
- **Contact Options**: Multiple contact methods including emergency services
- **Company Information**: Comprehensive about section with team and certifications

## 🌟 Key Features and Functionality

### 1. Header & Navigation
- Responsive navigation with mobile hamburger menu
- Smooth scrolling to page sections
- Sticky header that adapts on scroll
- Call-to-action button prominently displayed

### 2. Hero Section
- Compelling headline with gradient text effects
- Feature highlights with animated icons
- Multiple call-to-action buttons
- Statistics display with company achievements
- Animated visual elements and floating components

### 3. Services Section
- Four main service categories (Domestic, Commercial, Recycling, Grab Hire)
- Interactive service cards with feature lists
- "Most Popular" highlighting for recommended services
- Additional company benefits and contact information

### 4. Booking Form
- Comprehensive form with personal and service details
- Real-time validation with error messaging
- Skip size selection with visual cards
- Waste type and duration selection
- Success confirmation with booking reference

### 5. Pricing Display
- Category switching between Domestic and Commercial
- Detailed pricing cards with feature lists
- Special offers and discount information
- "Most Popular" plan highlighting
- Custom quote request options

### 6. About Section
- Company statistics and achievements
- Company story and mission
- Core values with detailed descriptions
- Team member profiles
- Certifications and compliance information

### 7. Contact Section
- Multiple contact methods (phone, email, address, hours)
- Interactive contact form with subject categorization
- Quick action buttons for immediate contact
- Emergency service information
- Map integration placeholder

### 8. Footer
- Comprehensive site navigation
- Company contact information
- Social media links
- Newsletter signup
- Legal links and certifications
- Emergency contact prominence

## 📱 Responsive Design

The application is fully responsive and optimized for:

### Desktop (1024px+)
- Full navigation menu with all links visible
- Multi-column layouts for optimal content display
- Large hero section with side-by-side content
- Grid layouts for services, pricing, and team sections

### Tablet (768px - 1023px)
- Adapted grid layouts (2-column instead of 4-column)
- Optimized spacing and typography
- Touch-friendly button sizes
- Maintained visual hierarchy

### Mobile (320px - 767px)
- Hamburger menu navigation
- Single-column layouts
- Stacked content sections
- Optimized form layouts
- Touch-optimized interactive elements

## 🔧 Development Notes

### Component Architecture
- **Modular Design**: Each component is self-contained with its own logic
- **Reusable Components**: Button and Card components used throughout
- **Props-Based Customization**: Flexible component APIs for different use cases
- **State Management**: Local state with React hooks for form handling

### Performance Optimizations
- **Vite Build System**: Fast development and optimized production builds
- **Code Splitting**: Automatic code splitting for optimal loading
- **Image Optimization**: Placeholder system for future image integration
- **CSS Optimization**: Tailwind's purge system removes unused styles

### Accessibility Features
- **Semantic HTML**: Proper heading hierarchy and landmark elements
- **Keyboard Navigation**: Full keyboard accessibility for all interactive elements
- **Color Contrast**: WCAG AA compliant color combinations
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Focus Management**: Visible focus indicators and logical tab order

## 🚀 Future Enhancements

### Potential Improvements
- **Backend Integration**: Connect forms to actual backend services
- **Payment Processing**: Integrate payment gateway for online bookings
- **User Accounts**: Customer portal for booking management
- **Real-time Tracking**: GPS tracking for skip deliveries
- **Image Gallery**: Before/after photos and service images
- **Blog Section**: Waste management tips and company news
- **Live Chat**: Customer support chat integration
- **Multi-language Support**: Internationalization for broader reach

### Technical Enhancements
- **TypeScript**: Add type safety for better development experience
- **Testing Suite**: Unit and integration tests with Jest/React Testing Library
- **PWA Features**: Service worker for offline functionality
- **SEO Optimization**: Meta tags, structured data, and sitemap
- **Analytics**: Google Analytics or similar for user behavior tracking
- **Performance Monitoring**: Real user monitoring and error tracking

## 📊 Project Statistics

- **Total Components**: 10 React components
- **Lines of Code**: ~2,500+ lines across all files
- **Development Time**: Completed in single session
- **Responsive Breakpoints**: 4 main breakpoints supported
- **Color Palette**: 20+ custom color variations
- **Animation Effects**: 6 custom Tailwind animations

## 🤝 Contributing

This project was created as a code challenge demonstration. For future development:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly across devices
5. Submit a pull request with detailed description

## 📄 License

This project is licensed under the ISC License - see the LICENSE file for details.

## 👨‍💻 Developer

Created by MorokaPrince as part of the WeWantWaste code challenge.

**Contact Information:**
- GitHub: [@MorokaPrince](https://github.com/MorokaPrince)
- Project Repository: [WeWantWaste CodeChallenge](https://github.com/MorokaPrince/-https-wewantwaste.co.uk-CodeChallenge-)

---

*This README was last updated on June 9, 2025, documenting the complete redesign of the WeWantWaste skip hire website.*
