{"version": 3, "names": ["_index", "require", "isSpecifierDefault", "specifier", "isImportDefaultSpecifier", "isIdentifier", "imported", "exported", "name"], "sources": ["../../src/validators/isSpecifierDefault.ts"], "sourcesContent": ["import { isIdentifier, isImportDefaultSpecifier } from \"./generated/index.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Check if the input `specifier` is a `default` import or export.\n */\nexport default function isSpecifierDefault(\n  specifier: t.ModuleSpecifier,\n): boolean {\n  return (\n    isImportDefaultSpecifier(specifier) ||\n    // @ts-expect-error todo(flow->ts): stricter type for specifier\n    isIdentifier(specifier.imported || specifier.exported, {\n      name: \"default\",\n    })\n  );\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAMe,SAASC,kBAAkBA,CACxCC,SAA4B,EACnB;EACT,OACE,IAAAC,+BAAwB,EAACD,SAAS,CAAC,IAEnC,IAAAE,mBAAY,EAACF,SAAS,CAACG,QAAQ,IAAIH,SAAS,CAACI,QAAQ,EAAE;IACrDC,IAAI,EAAE;EACR,CAAC,CAAC;AAEN", "ignoreList": []}