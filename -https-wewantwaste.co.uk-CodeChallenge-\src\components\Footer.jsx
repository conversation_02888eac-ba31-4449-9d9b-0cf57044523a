import React from 'react'
import Button from './Button'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    services: [
      { name: 'Domestic Skip Hire', href: '#services' },
      { name: 'Commercial Skip Hire', href: '#services' },
      { name: 'Grab Hire Services', href: '#services' },
      { name: 'Recycling Services', href: '#services' },
      { name: 'Waste Management', href: '#services' }
    ],
    company: [
      { name: 'About Us', href: '#about' },
      { name: 'Our Team', href: '#about' },
      { name: 'Careers', href: '#contact' },
      { name: 'Environmental Policy', href: '#about' },
      { name: 'Health & Safety', href: '#about' }
    ],
    support: [
      { name: 'Contact Us', href: '#contact' },
      { name: 'Get Quote', href: '#booking' },
      { name: 'Book Online', href: '#booking' },
      { name: 'Customer Support', href: '#contact' },
      { name: 'Emergency Service', href: '#contact' }
    ],
    legal: [
      { name: 'Privacy Policy', href: '#' },
      { name: 'Terms of Service', href: '#' },
      { name: 'Cookie Policy', href: '#' },
      { name: 'Waste Carrier License', href: '#' },
      { name: 'Insurance Details', href: '#' }
    ]
  }

  const socialLinks = [
    { name: 'Facebook', icon: '📘', href: 'https://facebook.com/wewantwaste' },
    { name: 'Twitter', icon: '🐦', href: 'https://twitter.com/wewantwaste' },
    { name: 'LinkedIn', icon: '💼', href: 'https://linkedin.com/company/wewantwaste' },
    { name: 'Instagram', icon: '📷', href: 'https://instagram.com/wewantwaste' }
  ]

  const scrollToSection = (href) => {
    if (href.startsWith('#')) {
      const element = document.querySelector(href)
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    } else {
      window.open(href, '_blank')
    }
  }

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer */}
      <div className="container-max section-padding">
        <div className="grid lg:grid-cols-5 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2 space-y-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-2xl">W</span>
              </div>
              <div>
                <h3 className="text-2xl font-bold">WeWantWaste</h3>
                <p className="text-gray-400">Skip Hire Solutions</p>
              </div>
            </div>
            
            <p className="text-gray-300 leading-relaxed">
              Professional skip hire and waste management services across the UK. 
              Committed to environmental responsibility and exceptional customer service since 2008.
            </p>
            
            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <span className="text-primary-400">📞</span>
                <span className="text-gray-300">+44 1234 567890</span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-primary-400">✉️</span>
                <span className="text-gray-300"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-primary-400">📍</span>
                <span className="text-gray-300">123 Waste Management Way, London, UK</span>
              </div>
            </div>

            {/* Emergency Contact */}
            <div className="bg-red-900/20 border border-red-800 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-red-400">🚨</span>
                <span className="font-semibold text-red-400">24/7 Emergency Service</span>
              </div>
              <p className="text-gray-300 text-sm mb-3">
                Urgent waste collection or emergency situations
              </p>
              <Button 
                size="sm" 
                variant="danger"
                onClick={() => window.open('tel:+441234567890')}
              >
                Call Emergency Line
              </Button>
            </div>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Services</h4>
            <ul className="space-y-3">
              {footerLinks.services.map((link, index) => (
                <li key={index}>
                  <button
                    onClick={() => scrollToSection(link.href)}
                    className="text-gray-300 hover:text-primary-400 transition-colors duration-200"
                  >
                    {link.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Company</h4>
            <ul className="space-y-3">
              {footerLinks.company.map((link, index) => (
                <li key={index}>
                  <button
                    onClick={() => scrollToSection(link.href)}
                    className="text-gray-300 hover:text-primary-400 transition-colors duration-200"
                  >
                    {link.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Support</h4>
            <ul className="space-y-3">
              {footerLinks.support.map((link, index) => (
                <li key={index}>
                  <button
                    onClick={() => scrollToSection(link.href)}
                    className="text-gray-300 hover:text-primary-400 transition-colors duration-200"
                  >
                    {link.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="mt-12 pt-8 border-t border-gray-800">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h4 className="text-xl font-semibold mb-2">Stay Updated</h4>
              <p className="text-gray-300">
                Get the latest news, tips, and special offers delivered to your inbox.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-white placeholder-gray-400"
              />
              <Button>Subscribe</Button>
            </div>
          </div>
        </div>

        {/* Social Links */}
        <div className="mt-8 pt-8 border-t border-gray-800">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex space-x-6">
              {socialLinks.map((social, index) => (
                <button
                  key={index}
                  onClick={() => window.open(social.href, '_blank')}
                  className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-primary-600 transition-colors duration-200"
                  title={social.name}
                >
                  <span className="text-lg">{social.icon}</span>
                </button>
              ))}
            </div>
            
            <div className="flex flex-wrap justify-center gap-6 text-sm">
              {footerLinks.legal.map((link, index) => (
                <button
                  key={index}
                  onClick={() => scrollToSection(link.href)}
                  className="text-gray-400 hover:text-primary-400 transition-colors duration-200"
                >
                  {link.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="bg-gray-950 py-6">
        <div className="container-max">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              © {currentYear} WeWantWaste. All rights reserved. | Waste Carrier License: WCL/123456
            </div>
            
            <div className="flex items-center space-x-6 text-sm">
              <div className="flex items-center space-x-2">
                <span className="text-green-400">🌱</span>
                <span className="text-gray-400">90% Waste Recycled</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-blue-400">🏆</span>
                <span className="text-gray-400">ISO 14001 Certified</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-yellow-400">⭐</span>
                <span className="text-gray-400">5-Star Rated</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
