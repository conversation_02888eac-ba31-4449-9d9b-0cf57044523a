{"name": "wewantwaste-redesign", "version": "1.0.0", "description": "Redesigning the whole page to look completely different from the original page while keeping its functionality intact.", "main": "index.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/MorokaPrince/-https-wewantwaste.co.uk-CodeChallenge-.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/MorokaPrince/-https-wewantwaste.co.uk-CodeChallenge-/issues"}, "homepage": "https://github.com/MorokaPrince/-https-wewantwaste.co.uk-CodeChallenge-#readme", "dependencies": {"@tailwindcss/postcss": "^4.1.8", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.5.1", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "vite": "^6.3.5"}}