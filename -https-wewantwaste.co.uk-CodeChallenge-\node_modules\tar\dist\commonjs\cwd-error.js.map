{"version": 3, "file": "cwd-error.js", "sourceRoot": "", "sources": ["../../src/cwd-error.ts"], "names": [], "mappings": ";;;AAAA,MAAa,QAAS,SAAQ,KAAK;IACjC,IAAI,CAAQ;IACZ,IAAI,CAAQ;IACZ,OAAO,GAAY,OAAO,CAAA;IAE1B,YAAY,IAAY,EAAE,IAAY;QACpC,KAAK,CAAC,GAAG,IAAI,qBAAqB,IAAI,GAAG,CAAC,CAAA;QAC1C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IAClB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,UAAU,CAAA;IACnB,CAAC;CACF;AAdD,4BAcC", "sourcesContent": ["export class CwdError extends Error {\n  path: string\n  code: string\n  syscall: 'chdir' = 'chdir'\n\n  constructor(path: string, code: string) {\n    super(`${code}: Cannot cd into '${path}'`)\n    this.path = path\n    this.code = code\n  }\n\n  get name() {\n    return 'CwdError'\n  }\n}\n"]}