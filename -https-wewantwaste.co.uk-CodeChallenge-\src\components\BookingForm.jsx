import React, { useState } from 'react'
import Card from './Card'
import Button from './Button'

const BookingForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    postcode: '',
    skipSize: '',
    wasteType: '',
    deliveryDate: '',
    duration: '',
    message: ''
  })

  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const skipSizes = [
    { value: '2-yard', label: '2 Yard Skip', description: 'Perfect for small household projects' },
    { value: '4-yard', label: '4 Yard Skip', description: 'Ideal for garden clearances' },
    { value: '6-yard', label: '6 Yard Skip', description: 'Great for home renovations' },
    { value: '8-yard', label: '8 Yard Skip', description: 'Large domestic projects' },
    { value: '12-yard', label: '12 Yard Skip', description: 'Commercial use' },
    { value: '20-yard', label: '20 Yard Skip', description: 'Large commercial projects' }
  ]

  const wasteTypes = [
    'General Household Waste',
    'Garden Waste',
    'Construction Waste',
    'Mixed Waste',
    'Soil & Rubble',
    'Wood & Timber',
    'Metal',
    'Other (specify in message)'
  ]

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.name.trim()) newErrors.name = 'Name is required'
    if (!formData.email.trim()) newErrors.email = 'Email is required'
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid'
    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required'
    if (!formData.address.trim()) newErrors.address = 'Address is required'
    if (!formData.postcode.trim()) newErrors.postcode = 'Postcode is required'
    if (!formData.skipSize) newErrors.skipSize = 'Please select a skip size'
    if (!formData.wasteType) newErrors.wasteType = 'Please select waste type'
    if (!formData.deliveryDate) newErrors.deliveryDate = 'Delivery date is required'
    if (!formData.duration) newErrors.duration = 'Hire duration is required'
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    setIsSubmitting(true)
    
    // Simulate API call
    try {
      await new Promise(resolve => setTimeout(resolve, 2000))
      setIsSubmitted(true)
      console.log('Booking submitted:', formData)
    } catch (error) {
      console.error('Submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isSubmitted) {
    return (
      <section id="booking" className="section-padding gradient-bg">
        <div className="container-max">
          <Card className="max-w-2xl mx-auto text-center">
            <div className="space-y-6">
              <div className="text-6xl">✅</div>
              <h2 className="text-3xl font-bold text-gray-900">Booking Submitted!</h2>
              <p className="text-gray-600">
                Thank you for your booking request. We'll contact you within 2 hours to confirm 
                your skip delivery details and provide a final quote.
              </p>
              <div className="bg-sky-50 rounded-lg p-4">
                <p className="text-sky-800 font-medium">
                  Booking Reference: #WW{Date.now().toString().slice(-6)}
                </p>
              </div>
              <Button onClick={() => setIsSubmitted(false)}>
                Make Another Booking
              </Button>
            </div>
          </Card>
        </div>
      </section>
    )
  }

  return (
    <section id="booking" className="section-padding gradient-bg">
      <div className="container-max">
        {/* Header */}
        <div className="text-center space-y-4 mb-12">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900">
            Book Your <span className="text-gradient">Skip</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Get an instant quote and book your skip hire service. 
            Fill out the form below and we'll get back to you within 2 hours.
          </p>
        </div>

        <Card className="max-w-4xl mx-auto">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Personal Information */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Personal Information</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-colors ${
                      errors.name ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter your full name"
                  />
                  {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors ${
                      errors.email ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter your email"
                  />
                  {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors ${
                      errors.phone ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter your phone number"
                  />
                  {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Postcode *
                  </label>
                  <input
                    type="text"
                    name="postcode"
                    value={formData.postcode}
                    onChange={handleInputChange}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors ${
                      errors.postcode ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="Enter your postcode"
                  />
                  {errors.postcode && <p className="text-red-500 text-sm mt-1">{errors.postcode}</p>}
                </div>
              </div>
              
              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Delivery Address *
                </label>
                <textarea
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  rows={3}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors ${
                    errors.address ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Enter full delivery address"
                />
                {errors.address && <p className="text-red-500 text-sm mt-1">{errors.address}</p>}
              </div>
            </div>

            {/* Skip Details */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Skip Details</h3>
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Skip Size *
                  </label>
                  <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {skipSizes.map((skip) => (
                      <label
                        key={skip.value}
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${
                          formData.skipSize === skip.value
                            ? 'border-sky-500 bg-sky-50'
                            : 'border-gray-300 hover:border-sky-300'
                        }`}
                      >
                        <input
                          type="radio"
                          name="skipSize"
                          value={skip.value}
                          checked={formData.skipSize === skip.value}
                          onChange={handleInputChange}
                          className="sr-only"
                        />
                        <div className="text-center">
                          <div className="font-semibold text-gray-900">{skip.label}</div>
                          <div className="text-sm text-gray-600">{skip.description}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                  {errors.skipSize && <p className="text-red-500 text-sm mt-1">{errors.skipSize}</p>}
                </div>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Waste Type *
                    </label>
                    <select
                      name="wasteType"
                      value={formData.wasteType}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors ${
                        errors.wasteType ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select waste type</option>
                      {wasteTypes.map((type) => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                    {errors.wasteType && <p className="text-red-500 text-sm mt-1">{errors.wasteType}</p>}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Hire Duration *
                    </label>
                    <select
                      name="duration"
                      value={formData.duration}
                      onChange={handleInputChange}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors ${
                        errors.duration ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select duration</option>
                      <option value="1-week">1 Week</option>
                      <option value="2-weeks">2 Weeks</option>
                      <option value="1-month">1 Month</option>
                      <option value="longer">Longer (specify in message)</option>
                    </select>
                    {errors.duration && <p className="text-red-500 text-sm mt-1">{errors.duration}</p>}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Delivery Date *
                  </label>
                  <input
                    type="date"
                    name="deliveryDate"
                    value={formData.deliveryDate}
                    onChange={handleInputChange}
                    min={new Date().toISOString().split('T')[0]}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors ${
                      errors.deliveryDate ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.deliveryDate && <p className="text-red-500 text-sm mt-1">{errors.deliveryDate}</p>}
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Additional Information
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                    placeholder="Any special requirements, access issues, or additional information..."
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="text-center">
              <Button
                type="submit"
                size="lg"
                disabled={isSubmitting}
                className="w-full sm:w-auto"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Booking Request'}
              </Button>
              <p className="text-sm text-gray-600 mt-4">
                * Required fields. We'll contact you within 2 hours to confirm your booking.
              </p>
            </div>
          </form>
        </Card>
      </div>
    </section>
  )
}

export default BookingForm
