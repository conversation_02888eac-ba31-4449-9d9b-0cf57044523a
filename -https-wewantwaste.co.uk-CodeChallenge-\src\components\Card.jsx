import React from 'react'

const Card = ({ 
  children, 
  className = '', 
  hover = true,
  padding = 'lg',
  ...props 
}) => {
  const baseClasses = 'bg-white rounded-xl shadow-lg transition-all duration-300'
  
  const hoverClasses = hover 
    ? 'hover:shadow-xl transform hover:-translate-y-1' 
    : ''
  
  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10',
    none: ''
  }
  
  const classes = `${baseClasses} ${hoverClasses} ${paddingClasses[padding]} ${className}`
  
  return (
    <div className={classes} {...props}>
      {children}
    </div>
  )
}

export default Card
