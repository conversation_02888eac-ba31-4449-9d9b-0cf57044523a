import React from 'react'
import <PERSON><PERSON> from './Button'

const Hero = () => {
  const scrollToSection = (href) => {
    const element = document.querySelector(href)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section id="home" className="pt-20 gradient-bg min-h-screen flex items-center">
      <div className="container-max section-padding">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8 animate-fade-in">
            <div className="space-y-4">
              <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Professional{' '}
                <span className="text-gradient">Skip Hire</span>{' '}
                Solutions
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed">
                Fast, reliable, and affordable skip hire services for all your waste management needs. 
                From home renovations to commercial projects, we've got you covered.
              </p>
            </div>

            {/* Features */}
            <div className="grid sm:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2 animate-slide-up animation-delay-200">
                <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-primary-600 text-sm">✓</span>
                </div>
                <span className="text-gray-700 font-medium">Same Day Delivery</span>
              </div>
              <div className="flex items-center space-x-2 animate-slide-up animation-delay-400">
                <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-primary-600 text-sm">✓</span>
                </div>
                <span className="text-gray-700 font-medium">Best Prices</span>
              </div>
              <div className="flex items-center space-x-2 animate-slide-up animation-delay-600">
                <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-primary-600 text-sm">✓</span>
                </div>
                <span className="text-gray-700 font-medium">Licensed & Insured</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 animate-slide-up animation-delay-600">
              <Button 
                size="lg"
                onClick={() => scrollToSection('#booking')}
              >
                Book Your Skip Now
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                onClick={() => scrollToSection('#pricing')}
              >
                View Pricing
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8 border-t border-gray-200">
              <div className="text-center animate-slide-up animation-delay-200">
                <div className="text-3xl font-bold text-primary-600">5000+</div>
                <div className="text-gray-600">Happy Customers</div>
              </div>
              <div className="text-center animate-slide-up animation-delay-400">
                <div className="text-3xl font-bold text-primary-600">24/7</div>
                <div className="text-gray-600">Support Available</div>
              </div>
              <div className="text-center animate-slide-up animation-delay-600">
                <div className="text-3xl font-bold text-primary-600">15+</div>
                <div className="text-gray-600">Years Experience</div>
              </div>
            </div>
          </div>

          {/* Image/Visual */}
          <div className="relative animate-fade-in animation-delay-400">
            <div className="relative z-10">
              <div className="bg-white rounded-2xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                <div className="aspect-square bg-gradient-to-br from-primary-100 to-secondary-100 rounded-xl flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <div className="w-24 h-24 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-full flex items-center justify-center mx-auto">
                      <span className="text-white text-4xl">🚛</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900">Quick Delivery</h3>
                      <p className="text-gray-600">Professional skip hire service</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 w-20 h-20 bg-secondary-200 rounded-full animate-bounce-gentle"></div>
            <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-primary-200 rounded-full animate-bounce-gentle animation-delay-400"></div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
